import { Response, NextFunction } from 'express';
import { verifySupabaseToken } from '../config/supabase';

export const authenticateUser = async (req: any, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization as string | undefined;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.warn('[Auth] No token provided', { ip: req.ip, path: req.originalUrl });
      return res.status(401).json({
        success: false,
        error: 'No token provided',
        code: 'auth/no-token',
        timestamp: new Date().toISOString(),
      });
    }

    const token = authHeader.split('Bearer ')[1];

    try {
      // Verify token with Supabase
      const { user, error } = await verifySupabaseToken(token);

      if (error || !user) {
        console.error('[Auth] Supabase token verification failed', {
          error: error?.message,
          ip: req.ip,
          path: req.originalUrl,
          tokenSnippet: token ? token.slice(0, 10) + '...' : undefined,
        });
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token',
          code: 'auth/invalid-token',
          timestamp: new Date().toISOString(),
        });
      }

      // Set user context for downstream middleware and controllers
      req.user = {
        uid: user.id,
        email: user.email || '',
        user_metadata: user.user_metadata || {},
        app_metadata: user.app_metadata || {}
      };

      console.log('[Auth] User authenticated successfully', {
        userId: user.id,
        email: user.email,
        path: req.originalUrl
      });

      next();
    } catch (error: any) {
      // Log error with debugging info, but do not expose sensitive details
      console.error('[Auth] Token verification failed', {
        message: error?.message,
        name: error?.name,
        ip: req.ip,
        path: req.originalUrl,
        tokenSnippet: token ? token.slice(0, 10) + '...' : undefined,
      });
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
        code: 'auth/invalid-token',
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error: any) {
    // Log unexpected errors
    console.error('[Auth] Authentication middleware error', {
      message: error?.message,
      stack: error?.stack,
      ip: req.ip,
      path: req.originalUrl,
    });
    return res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'auth/internal-error',
      timestamp: new Date().toISOString(),
    });
  }
};

// Optional middleware for routes that don't require authentication but can use user context if available
export const optionalAuth = async (req: any, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization as string | undefined;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without user context
      req.user = null;
      return next();
    }

    const token = authHeader.split('Bearer ')[1];

    try {
      const { user, error } = await verifySupabaseToken(token);

      if (error || !user) {
        // Invalid token, continue without user context
        req.user = null;
      } else {
        // Valid token, set user context
        req.user = {
          uid: user.id,
          email: user.email || '',
          user_metadata: user.user_metadata || {},
          app_metadata: user.app_metadata || {}
        };
      }
    } catch (error) {
      // Token verification failed, continue without user context
      req.user = null;
    }

    next();
  } catch (error: any) {
    // Log unexpected errors but don't block the request
    console.error('[Auth] Optional auth middleware error', {
      message: error?.message,
      ip: req.ip,
      path: req.originalUrl,
    });
    req.user = null;
    next();
  }
};